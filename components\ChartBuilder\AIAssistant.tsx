'use client'

import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Sparkles,
  Send,
  X,
  Copy,
  Check,
  Database,
  Code,
  Lightbulb,
  Zap,
  ChevronDown,
  ChevronUp,
  Play,
  RotateCcw
} from "lucide-react"
import { toast } from "sonner"
import { Dataset } from '@/types/index'

interface AIAssistantProps {
  selectedDatasets: Dataset[]
  language: 'sql' | 'python' | 'javascript' | 'markdown'
  onCodeGenerated: (code: string) => void
}

interface ConversationMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  code?: string
  timestamp: Date
}

export function AIAssistant({ selectedDatasets, language, onCodeGenerated }: AIAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [prompt, setPrompt] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [generatedCode, setGeneratedCode] = useState('')
  const [copied, setCopied] = useState(false)
  const [conversation, setConversation] = useState<ConversationMessage[]>([])
  const [showDatasetDetails, setShowDatasetDetails] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'
    }
  }, [prompt])

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [conversation])

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error('Please enter a prompt')
      return
    }

    if (selectedDatasets.length === 0) {
      toast.error('Please select at least one dataset first')
      return
    }

    // Add user message to conversation
    const userMessage: ConversationMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: prompt,
      timestamp: new Date()
    }
    setConversation(prev => [...prev, userMessage])

    setIsLoading(true)
    const currentPrompt = prompt
    setPrompt('') // Clear input immediately for better UX

    try {
      // Prepare enhanced dataset information for the AI
      const datasetsInfo = selectedDatasets.map(ds => ({
        name: ds.name,
        columns: ds.headers,
        sampleData: ds.data.slice(0, 3), // First 3 rows as sample
        rowCount: ds.data.length,
        // Add data type analysis
        columnTypes: ds.headers?.map(header => {
          const sampleValues = ds.data.slice(0, 10).map(row => row[header]).filter(val => val != null)
          const isNumeric = sampleValues.every(val => !isNaN(Number(val)) && val !== '')
          const isDate = sampleValues.some(val => !isNaN(Date.parse(val)))
          return {
            name: header,
            type: isNumeric ? 'numeric' : isDate ? 'date' : 'text',
            sampleValues: sampleValues.slice(0, 3)
          }
        })
      }))

      const response = await fetch('/api/ai/chartbuilder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: currentPrompt,
          language: language,
          datasets: datasetsInfo
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate code')
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Failed to generate code')
      }

      const generatedCode = data.code || ''
      setGeneratedCode(generatedCode)

      // Add assistant response to conversation
      const assistantMessage: ConversationMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `I've generated ${language.toUpperCase()} code for your request. The code is ready to run with your selected datasets.`,
        code: generatedCode,
        timestamp: new Date()
      }
      setConversation(prev => [...prev, assistantMessage])

      toast.success('Code generated successfully!')
    } catch (error) {
      console.error('Error generating code:', error)

      // Add error message to conversation
      const errorMessage: ConversationMessage = {
        id: (Date.now() + 2).toString(),
        type: 'assistant',
        content: `Sorry, I encountered an error while generating the code: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again with a different prompt.`,
        timestamp: new Date()
      }
      setConversation(prev => [...prev, errorMessage])

      toast.error('Failed to generate code. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCopy = async (code?: string) => {
    try {
      const textToCopy = code || generatedCode
      await navigator.clipboard.writeText(textToCopy)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast.success('Code copied to clipboard!')
    } catch (error) {
      toast.error('Failed to copy code')
    }
  }

  const handleUseCode = (code: string) => {
    onCodeGenerated(code)
    toast.success('Code inserted into cell!')
  }

  const handleClearConversation = () => {
    setConversation([])
    setGeneratedCode('')
    toast.success('Conversation cleared')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      handleGenerate()
    }
  }

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        size="sm"
        variant="outline"
        className="gap-2 bg-gradient-to-r from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100 border-blue-200 text-blue-700 hover:text-blue-800 transition-all duration-200"
      >
        <Sparkles className="h-4 w-4" />
        AI Assistant
      </Button>
    )
  }

  return (
    <Card className="w-full max-w-4xl h-[600px] flex flex-col bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-950 border-blue-200 shadow-xl">
      {/* Header */}
      <CardHeader className="pb-3 border-b bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
              <Sparkles className="h-5 w-5 text-white" />
            </div>
            <div>
              <div className="text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Databricks AI Assistant
              </div>
              <div className="text-xs text-muted-foreground font-normal">
                Powered by Gemini • {language.toUpperCase()} • {selectedDatasets.length} dataset{selectedDatasets.length !== 1 ? 's' : ''}
              </div>
            </div>
          </CardTitle>
          <div className="flex items-center gap-2">
            {conversation.length > 0 && (
              <Button
                onClick={handleClearConversation}
                size="sm"
                variant="ghost"
                className="gap-2 text-muted-foreground hover:text-foreground"
              >
                <RotateCcw className="h-4 w-4" />
                Clear
              </Button>
            )}
            <Button
              onClick={() => setIsOpen(false)}
              size="sm"
              variant="ghost"
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Dataset Info */}
        <div className="mt-3">
          <Button
            onClick={() => setShowDatasetDetails(!showDatasetDetails)}
            variant="ghost"
            size="sm"
            className="gap-2 text-xs text-muted-foreground hover:text-foreground p-0 h-auto"
          >
            <Database className="h-3 w-3" />
            {selectedDatasets.length} Connected Dataset{selectedDatasets.length !== 1 ? 's' : ''}
            {showDatasetDetails ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
          </Button>

          {showDatasetDetails && (
            <div className="mt-2 p-3 bg-white/70 dark:bg-slate-800/70 rounded-lg border">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {selectedDatasets.map((ds, index) => (
                  <div key={index} className="flex items-center gap-2 text-xs">
                    <Badge variant="outline" className="text-xs">
                      {ds.name}
                    </Badge>
                    <span className="text-muted-foreground">
                      {ds.data?.length || 0} rows • {ds.headers?.length || 0} cols
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardHeader>

      {/* Conversation Area */}
      <CardContent className="flex-1 flex flex-col p-0">
        <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
          {conversation.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
              <div className="p-4 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full">
                <Lightbulb className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground mb-2">
                  Welcome to AI Assistant
                </h3>
                <p className="text-sm text-muted-foreground max-w-md">
                  I can help you analyze your data, create visualizations, and write {language.toUpperCase()} code.
                  Try asking me to explore your datasets or create specific analyses.
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 w-full max-w-lg">
                {[
                  "Show me summary statistics for all columns",
                  "Create a correlation heatmap",
                  "Find top 10 records by value",
                  "Generate a bar chart of categories"
                ].map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="text-xs justify-start"
                    onClick={() => setPrompt(suggestion)}
                  >
                    <Zap className="h-3 w-3 mr-2" />
                    {suggestion}
                  </Button>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {conversation.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      message.type === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white dark:bg-slate-800 border'
                    }`}
                  >
                    <div className="text-sm">{message.content}</div>
                    {message.code && (
                      <div className="mt-3 space-y-2">
                        <Separator />
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Code className="h-3 w-3" />
                            Generated {language.toUpperCase()} Code
                          </div>
                          <div className="flex gap-1">
                            <Button
                              onClick={() => handleCopy(message.code!)}
                              size="sm"
                              variant="ghost"
                              className="h-6 px-2 text-xs"
                            >
                              {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                            </Button>
                            <Button
                              onClick={() => handleUseCode(message.code!)}
                              size="sm"
                              variant="ghost"
                              className="h-6 px-2 text-xs gap-1"
                            >
                              <Play className="h-3 w-3" />
                              Use
                            </Button>
                          </div>
                        </div>
                        <pre className="bg-slate-100 dark:bg-slate-900 p-3 rounded text-xs overflow-x-auto">
                          <code>{message.code}</code>
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-white dark:bg-slate-800 border rounded-lg p-3 max-w-[80%]">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      Generating {language.toUpperCase()} code...
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </ScrollArea>

        {/* Input Area */}
        <div className="border-t bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm p-4">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Textarea
                ref={textareaRef}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={`Ask me anything about your data... (${language.toUpperCase()})`}
                className="min-h-[40px] max-h-[120px] resize-none pr-12 bg-white dark:bg-slate-900"
                disabled={isLoading}
              />
              <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                ⌘+Enter
              </div>
            </div>
            <Button
              onClick={handleGenerate}
              disabled={isLoading || !prompt.trim()}
              className="self-end bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
