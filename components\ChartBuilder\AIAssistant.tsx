'use client'

import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Sparkles,
  Send,
  X,
  Copy,
  Check,
  Database,
  Code,
  Lightbulb,
  Zap,
  ChevronDown,
  ChevronUp,
  Play,
  RotateCcw,
  BarChart3,
  TrendingUp,
  Award,
  Shield,
  MessageSquare,
  Wand2
} from "lucide-react"
import { toast } from "sonner"
import { Dataset } from '@/types/index'

interface AIAssistantProps {
  selectedDatasets: Dataset[]
  language: 'sql' | 'python' | 'javascript' | 'markdown'
  onCodeGenerated: (code: string) => void
}

interface ConversationMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  code?: string
  timestamp: Date
}

export function AIAssistant({ selectedDatasets, language, onCodeGenerated }: AIAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [prompt, setPrompt] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [generatedCode, setGeneratedCode] = useState('')
  const [copied, setCopied] = useState(false)
  const [showQuickActions, setShowQuickActions] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'
    }
  }, [prompt])

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [conversation])

  const handleGenerate = async (quickPrompt?: string) => {
    const currentPrompt = quickPrompt || prompt.trim()

    if (!currentPrompt) {
      toast.error('Please enter a prompt')
      return
    }

    if (selectedDatasets.length === 0) {
      toast.error('Please select at least one dataset first')
      return
    }

    setIsLoading(true)
    if (!quickPrompt) {
      setPrompt('') // Clear input only if not using quick action
    }

    try {
      // Prepare enhanced dataset information for the AI
      const datasetsInfo = selectedDatasets.map(ds => ({
        name: ds.name,
        columns: ds.headers,
        sampleData: ds.data.slice(0, 3),
        rowCount: ds.data.length,
        columnTypes: ds.headers?.map(header => {
          const sampleValues = ds.data.slice(0, 10).map(row => row[header]).filter(val => val != null)
          const isNumeric = sampleValues.every(val => !isNaN(Number(val)) && val !== '')
          const isDate = sampleValues.some(val => !isNaN(Date.parse(val)))
          return {
            name: header,
            type: isNumeric ? 'numeric' : isDate ? 'date' : 'text',
            sampleValues: sampleValues.slice(0, 3)
          }
        })
      }))

      const response = await fetch('/api/ai/chartbuilder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: currentPrompt,
          language: language,
          datasets: datasetsInfo
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate code')
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Failed to generate code')
      }

      setGeneratedCode(data.code || '')
      setIsExpanded(true) // Show the result
      toast.success('Code generated successfully!')
    } catch (error) {
      console.error('Error generating code:', error)
      toast.error('Failed to generate code. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCopy = async (code?: string) => {
    try {
      const textToCopy = code || generatedCode
      await navigator.clipboard.writeText(textToCopy)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast.success('Code copied to clipboard!')
    } catch (error) {
      toast.error('Failed to copy code')
    }
  }

  const handleUseCode = () => {
    onCodeGenerated(generatedCode)
    setIsOpen(false)
    setIsExpanded(false)
    setGeneratedCode('')
    toast.success('Code inserted into cell!')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      handleGenerate()
    }
    if (e.key === 'Escape') {
      setIsOpen(false)
      setIsExpanded(false)
    }
  }

  // Quick action prompts
  const quickActions = [
    { label: "Summary", prompt: "Show summary statistics for all columns", icon: BarChart3 },
    { label: "Chart", prompt: "Create a visualization showing the main trends", icon: TrendingUp },
    { label: "Top 10", prompt: "Show the top 10 records by the most important metric", icon: Award },
    { label: "Clean", prompt: "Check for missing values and data quality issues", icon: Shield }
  ]

  if (!isOpen) {
    return (
      <div className="relative">
        <Button
          onClick={() => setIsOpen(true)}
          size="sm"
          variant="ghost"
          className="gap-1.5 h-7 px-2 text-xs bg-gradient-to-r from-violet-50 to-blue-50 hover:from-violet-100 hover:to-blue-100 border border-violet-200/50 text-violet-700 hover:text-violet-800 transition-all duration-200 shadow-sm"
        >
          <Wand2 className="h-3.5 w-3.5" />
          Magic
        </Button>
      </div>
    )
  }

  return (
    <div className="relative" ref={containerRef}>
      {/* Compact AI Panel */}
      <div className="absolute top-0 left-0 right-0 z-50 bg-white dark:bg-slate-900 border border-violet-200 dark:border-violet-800 rounded-lg shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-violet-100 dark:border-violet-800">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-gradient-to-r from-violet-500 to-blue-500 rounded-md">
              <Wand2 className="h-3.5 w-3.5 text-white" />
            </div>
            <div>
              <div className="text-sm font-medium text-violet-900 dark:text-violet-100">
                AI Magic
              </div>
              <div className="text-xs text-violet-600 dark:text-violet-400">
                {language.toUpperCase()} • {selectedDatasets.length} dataset{selectedDatasets.length !== 1 ? 's' : ''}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button
              onClick={() => setShowQuickActions(!showQuickActions)}
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs text-violet-600 hover:text-violet-800"
            >
              <Zap className="h-3 w-3" />
            </Button>
            <Button
              onClick={() => setIsOpen(false)}
              size="sm"
              variant="ghost"
              className="h-6 px-2 text-xs text-violet-600 hover:text-violet-800"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        {showQuickActions && (
          <div className="p-3 border-b border-violet-100 dark:border-violet-800">
            <div className="grid grid-cols-2 gap-2">
              {quickActions.map((action, index) => {
                const IconComponent = action.icon
                return (
                  <Button
                    key={index}
                    onClick={() => handleGenerate(action.prompt)}
                    disabled={isLoading}
                    size="sm"
                    variant="outline"
                    className="h-8 text-xs justify-start gap-2 border-violet-200 hover:border-violet-300 hover:bg-violet-50"
                  >
                    <IconComponent className="h-3 w-3" />
                    {action.label}
                  </Button>
                )
              })}
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="p-3">
          <div className="flex gap-2">
            <div className="flex-1">
              <Textarea
                ref={textareaRef}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={`Ask me anything about your data...`}
                className="min-h-[32px] max-h-[80px] resize-none text-sm border-violet-200 focus:border-violet-400 focus:ring-violet-400"
                disabled={isLoading}
              />
            </div>
            <Button
              onClick={() => handleGenerate()}
              disabled={isLoading || !prompt.trim()}
              size="sm"
              className="self-end bg-gradient-to-r from-violet-500 to-blue-500 hover:from-violet-600 hover:to-blue-600 h-8"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
              ) : (
                <Send className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>

        {/* Generated Code Result */}
        {isExpanded && generatedCode && (
          <div className="border-t border-violet-100 dark:border-violet-800 p-3 bg-violet-50/50 dark:bg-violet-900/20">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2 text-xs text-violet-700 dark:text-violet-300">
                <Code className="h-3 w-3" />
                Generated {language.toUpperCase()} Code
              </div>
              <div className="flex gap-1">
                <Button
                  onClick={() => handleCopy()}
                  size="sm"
                  variant="ghost"
                  className="h-6 px-2 text-xs text-violet-600 hover:text-violet-800"
                >
                  {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                </Button>
                <Button
                  onClick={handleUseCode}
                  size="sm"
                  variant="ghost"
                  className="h-6 px-2 text-xs gap-1 text-violet-600 hover:text-violet-800"
                >
                  <Play className="h-3 w-3" />
                  Use
                </Button>
              </div>
            </div>
            <pre className="bg-white dark:bg-slate-900 p-2 rounded text-xs overflow-x-auto border border-violet-200 dark:border-violet-700 max-h-32">
              <code>{generatedCode}</code>
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
