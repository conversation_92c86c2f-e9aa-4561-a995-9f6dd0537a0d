'use client'

import { useState, useEffect, useMemo, Suspense, lazy } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Cell } from './Cell'
import { Button } from "@/components/ui/button"
import { Plus, Database, ChevronDown, Code2, FileText, RefreshCw, FolderOpen } from "lucide-react"
import { nanoid } from 'nanoid'
import { toast } from 'sonner'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { NotebookExporter } from './NotebookExporter'
import { SavedChart, TableItem, PythonPlotItem } from './DashboardSection/types' // Import for type compatibility
import { Dataset } from '@/types/index'
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useDashboardStore } from '@/lib/dashboardStore'
import { MiniSidebar, DatasetItem } from '@/components/Datanalytics/DataEditorComponents/MiniSidebar'
import { WorkspaceSelector } from './WorkspaceSelector'
import { ChartBuilderHeader } from './ChartBuilderHeader'
import dynamic from 'next/dynamic'

// Lazy load the Dashboard component for better performance
// Use a simple loading state
const Dashboard = dynamic(() => import('./DashboardSection/Dashboard').then(mod => mod.default), {
  loading: () => (
    <div className="flex items-center justify-center h-[calc(100vh-100px)]">
      <div className="text-center space-y-6">
        <div className="relative">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary mx-auto"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 bg-primary rounded-full animate-pulse"></div>
          </div>
        </div>
        <div className="space-y-3">
          <div className="text-xl font-semibold">Loading Dashboard</div>
          <div className="text-sm text-muted-foreground">Preparing your analytics workspace...</div>
          <div className="flex items-center justify-center space-x-1">
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
        <div className="grid grid-cols-3 gap-4 max-w-md mx-auto">
          <div className="h-20 bg-muted rounded-lg animate-pulse"></div>
          <div className="h-20 bg-muted rounded-lg animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="h-20 bg-muted rounded-lg animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
      </div>
    </div>
  ),
  ssr: false // Disable server-side rendering for this component
})

// Import logic components from chartbuilderlogic folder
import {
  useDatasetHandling,
  useCellExecution,
  useChartSaving,
  useDashboardInteraction,
  getDefaultContent,
  checkInternetConnection,
  CellData,
  QueryResult
} from './chartbuilderlogic'
import { useWorkspaceManagement } from './chartbuilderlogic/useWorkspaceManagement'

export default function ChartBuilder() {
  const [cells, setCells] = useState<CellData[]>([
    {
      id: nanoid(),
      content: '-- Select datasets using the database icon, then write your SQL query\n-- Selected datasets are available using their actual names\n-- Example: SELECT * FROM employees LIMIT 5;',
      language: 'sql',
      selectedDatasetIds: [] // Initialize with empty dataset selection
    }
  ])
  const [currentData, setCurrentData] = useState<QueryResult[]>([])

  // Use Zustand store for dashboard items
  const {
    charts: savedCharts,
    tables: savedTables,
    plots: savedPlots,
    calculatorResults: savedCalculatorResults
  } = useDashboardStore()

  const [isOnline, setIsOnline] = useState<boolean>(true) // Track online status
  const [isCheckingConnection, setIsCheckingConnection] = useState<boolean>(true) // Track if we're checking connection
  const [showOnlineStatus, setShowOnlineStatus] = useState<boolean>(false) // Show online status temporarily
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(false) // Track sidebar collapse state

  // Use URL search parameters for tab state and workspace
  const router = useRouter()
  const searchParams = useSearchParams()
  const tabParam = searchParams.get('tab')
  const workspaceParam = searchParams.get('workspace')
  const [activeTab, setActiveTab] = useState<string>(tabParam === 'dashboard' ? 'dashboard' : 'notebook')

  // Sync activeTab with URL on mount and when URL changes
  useEffect(() => {
    const newTab = tabParam === 'dashboard' ? 'dashboard' : 'notebook';
    setActiveTab(newTab);
  }, [tabParam]);

  // Preload the Dashboard component when the component mounts
  useEffect(() => {
    // Simple preload without complex logic
    if (typeof window !== 'undefined') {
      // Use a short timeout to avoid blocking initial render
      setTimeout(() => {
        import('./DashboardSection/Dashboard').then(mod => {
          // Just preload, don't do anything with the result
          console.log('Dashboard component preloaded');
        }).catch(err => {
          // Silently handle any errors during preloading
          console.error('Error preloading Dashboard component:', err);
        });
      }, 500);
    }
  }, []);

  // Use the dataset handling hook
  const {
    datasets,
    datasetCache,
    isLoadingDatasets,
    handleSelectDatasets,
    refreshDatasets
  } = useDatasetHandling()

  // Use the cell execution hook
  const {
    handleRunCell,
    isAlasqlInitialized
  } = useCellExecution(setCells, cells, datasetCache)

  // Use the chart saving hook
  const {
    handleSaveChart,
    handleSaveTable,
    handleSavePlot,
    handleSaveCalculatorResult,
    handleRemoveChart,
    handleUpdateChart,
    handleReorderCharts
  } = useChartSaving()

  // Use the dashboard interaction hook
  const {
    handleNotebookImport,
    handleImportChartConfig
  } = useDashboardInteraction(setCells, cells, datasets, handleSelectDatasets)

  // Use the workspace management hook
  const {
    currentWorkspace,
    isSaving,
    isLoadingCells,
    isLoadingDashboard,
    handleWorkspaceChange,
    saveCurrentStateToWorkspace,
    loadWorkspaceState
  } = useWorkspaceManagement()

  // Load workspace from URL parameter if provided
  useEffect(() => {
    if (workspaceParam) {
      loadWorkspaceState(workspaceParam, setCells);
    }
  }, [workspaceParam, loadWorkspaceState]);

  // Manual connectivity check function
  const checkConnectivity = async () => {
    try {
      setIsCheckingConnection(true);
      const isConnected = await checkInternetConnection();
      const wasOnline = isOnline;
      setIsOnline(isConnected);
      console.log(`Connectivity check: ${isConnected ? 'ONLINE' : 'OFFLINE'}`);

      // Show status messages
      if (isConnected && !wasOnline) {
        setShowOnlineStatus(true);
        setTimeout(() => setShowOnlineStatus(false), 3000); // Hide after 3 seconds
        toast.success('Connection restored!');
      } else if (!isConnected) {
        toast.error('Server unreachable - please check your connection');
      }
    } catch (error) {
      console.error('Error checking connectivity:', error);
      setIsOnline(false);
      toast.error('Connection check failed');
    } finally {
      setIsCheckingConnection(false);
    }
  };

  // Initial connectivity check on mount
  useEffect(() => {
    checkConnectivity();

    // Also listen to browser online/offline events as backup
    const handleOnline = () => {
      // When browser says we're online, verify with our API
      checkConnectivity();
    };
    const handleOffline = () => {
      // When browser says we're offline, trust it immediately
      setIsOnline(false);
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      }
    };
  }, []);

  // Handle saving current state to workspace
  const handleSaveToWorkspace = async () => {
    if (!currentWorkspace) {
      toast.error('No workspace selected')
      return
    }

    // Show loading toast
    const loadingToast = toast.loading('Saving notebook and dashboard to workspace...')

    try {
      // Debug: Log store state
      console.log('Dashboard store state:', {
        savedCharts: savedCharts?.length || 0,
        savedTables: savedTables?.length || 0,
        savedPlots: savedPlots?.length || 0,
        savedCalculatorResults: savedCalculatorResults?.length || 0
      })

      // Prepare dashboard items from the store with null checks
      const dashboardItems = [
        ...(savedCharts || []).map(chart => ({
          type: 'chart',
          title: chart.title || chart.config?.title,
          description: chart.description,
          data: chart.data,
          config: chart.config,
          gridColumn: chart.gridColumn,
          gridRow: chart.gridRow,
          width: chart.width,
          height: chart.height
        })),
        ...(savedTables || []).map(table => ({
          type: 'table',
          title: table.title,
          description: table.description,
          data: table.data,
          config: table.config,
          gridColumn: table.gridColumn,
          gridRow: table.gridRow,
          width: table.width,
          height: table.height
        })),
        ...(savedPlots || []).map(plot => ({
          type: 'pythonplot',
          title: plot.title,
          description: plot.description,
          data: { plotUrl: plot.plotUrl },
          gridColumn: plot.gridColumn,
          gridRow: plot.gridRow,
          width: plot.width,
          height: plot.height
        })),
        ...(savedCalculatorResults || []).map(result => ({
          type: 'calculator',
          title: result.title,
          description: result.description,
          data: {
            result: result.result,
            formula: result.formula,
            resultType: result.resultType,
            formattedResult: result.formattedResult,
            icon: result.icon
          },
          config: {
            formula: result.formula,
            icon: result.icon
          },
          timestamp: result.timestamp,
          gridColumn: result.gridColumn,
          gridRow: result.gridRow,
          width: result.width,
          height: result.height
        }))
      ]

    await saveCurrentStateToWorkspace(cells, dashboardItems)

    // Dismiss loading toast
    toast.dismiss(loadingToast)

    } catch (error) {
      toast.dismiss(loadingToast)
      console.error('Error saving to workspace:', error)
      toast.error('Failed to save to workspace')
    }
  }




  // Use the getDefaultContent function from chartbuilderlogic
  const getDefaultMarkdownContent = () => {
    return getDefaultContent('markdown');
  }

  // Update handleAddCell to support different cell types
  const handleAddCell = (afterId?: string, cellType: 'code' | 'markdown' = 'code') => {
    setCells(prev => {
      const newCell = {
        id: nanoid(),
        content: cellType === 'markdown' ? getDefaultMarkdownContent() : getDefaultContent('sql'),
        language: cellType === 'markdown' ? 'markdown' : 'sql',
        cellType: cellType,
        selectedDatasetIds: [] // Initialize with empty dataset selection
      }

      if (!afterId || prev.length === 0) {
        return [...prev, newCell]
      }

      const index = prev.findIndex(cell => cell.id === afterId)
      return [
        ...prev.slice(0, index + 1),
        newCell,
        ...prev.slice(index + 1)
      ]
    })
  }

  // Delete cell
  const handleDeleteCell = (cellId: string) => {
    setCells(prev => prev.filter(cell => cell.id !== cellId))
  }

  const handleLanguageChange = (cellId: string, language: string) => {
    setCells(prev => {
      const cell = prev.find(c => c.id === cellId);
      if (!cell) return prev;

      // Only replace content if it's empty or matches the default for the previous language
      const currentContent = cell.content || '';
      const defaultForOldLang = getDefaultContent(cell.language as any);
      const shouldReplaceContent = !currentContent.trim() ||
                                  currentContent === defaultForOldLang ||
                                  // Also replace if switching between languages and content has default markers
                                  (language === 'python' && currentContent.includes('-- Write your SQL query here')) ||
                                  (language === 'sql' && currentContent.includes('# Write your Python code here'));

      return prev.map(c =>
        c.id === cellId
          ? {
              ...c,
              language,
              content: shouldReplaceContent ? getDefaultContent(language as any) : c.content,
              // Clear any previous results and errors when changing language
              result: undefined,
              error: undefined,
              errorDetails: undefined,
              isSuccess: undefined
            }
          : c
      );
    });

    // Log the language change for debugging
    console.log(`Language changed for cell ${cellId} to ${language}`);
  }



  // Handle updating notes for a cell
  const handleUpdateNotes = (cellId: string, notes: string) => {
    setCells(prev => prev.map(cell =>
      cell.id === cellId ? { ...cell, notes } : cell
    ));

    // Save to localStorage or other persistence mechanism if needed
    toast.success("Notes updated");
  };

  // Handle drag and drop reordering of cells
  const handleDragEnd = (result: DropResult) => {
    const { destination, source } = result;

    // If there's no destination or the item was dropped back in its original position
    if (!destination ||
        (destination.droppableId === source.droppableId &&
         destination.index === source.index)) {
      return;
    }

    // Reorder the cells array
    const reorderedCells = Array.from(cells);
    const [movedCell] = reorderedCells.splice(source.index, 1);
    reorderedCells.splice(destination.index, 0, movedCell);

    // Update the state with the new order
    setCells(reorderedCells);
    toast.success(`Cell moved to position ${destination.index + 1}`);
  };

  // Move a cell up in the order
  const moveCellUp = (cellId: string) => {
    const index = cells.findIndex(cell => cell.id === cellId);
    if (index <= 0) return; // Already at the top

    const reorderedCells = Array.from(cells);
    const [movedCell] = reorderedCells.splice(index, 1);
    reorderedCells.splice(index - 1, 0, movedCell);

    setCells(reorderedCells);
    toast.success(`Cell moved up`);
  };

  // Move a cell down in the order
  const moveCellDown = (cellId: string) => {
    const index = cells.findIndex(cell => cell.id === cellId);
    if (index === -1 || index >= cells.length - 1) return; // Already at the bottom

    const reorderedCells = Array.from(cells);
    const [movedCell] = reorderedCells.splice(index, 1);
    reorderedCells.splice(index + 1, 0, movedCell);

    setCells(reorderedCells);
    toast.success(`Cell moved down`);
  };

  // Add convertCellType function after moveCellDown function
  const convertCellType = (cellId: string, targetType: 'code' | 'markdown') => {
    setCells(prev => {
      // Find the cell
      const cellIndex = prev.findIndex(cell => cell.id === cellId);
      if (cellIndex === -1) return prev;

      const cell = prev[cellIndex];

      // If already the target type, do nothing
      if (cell.cellType === targetType) return prev;

      // Create default content for the new type
      let newContent = '';
      let newLanguage = cell.language;

      if (targetType === 'markdown') {
        // Convert code to markdown
        newContent = `# Code Snippet\n\`\`\`${cell.language}\n${cell.content}\n\`\`\``;
        newLanguage = 'markdown';
      } else {
        // Convert markdown to code
        newContent = getDefaultContent('sql');
        newLanguage = 'sql';
      }

      // Return updated cells array
      return prev.map((c, i) =>
        i === cellIndex
          ? {
              ...c,
              cellType: targetType,
              content: newContent,
              language: newLanguage,
              // Clear result data when converting a code cell to markdown
              result: targetType === 'markdown' ? undefined : c.result,
              error: targetType === 'markdown' ? undefined : c.error,
              errorDetails: targetType === 'markdown' ? undefined : c.errorDetails
            }
          : c
      );
    });

    toast.success(`Cell converted to ${targetType} cell`);
  };

  // Memoize the datasets to prevent unnecessary re-renders of MiniSidebar
  const memoizedDatasets = useMemo(() => {
    console.log('[ChartBuilder] Memoizing datasets, count:', datasets.length);
    return datasets.map(ds => ({
      ...ds,
      createdAt: ds.createdAt.toISOString(), // Convert Date to string
      data: ds.data || [],
      headers: ds.headers || [],
      // description, fileType, folderId should align or be explicitly mapped
    }));
  }, [datasets]); // Only re-compute when datasets array reference changes

  // Calculate storage info for MiniSidebar
  const storageInfoValue = useMemo(() => {
    console.log('[ChartBuilder] Datasets for storage calculation:', JSON.stringify(datasets.map(d => ({ id: d.id, name: d.name, dataLength: d.data?.length, fileType: d.fileType }))));
    const usedInBytes = datasets.reduce((acc, dataset) => {
      const datasetData = dataset.data || [];
      const currentDatasetSizeBytes = JSON.stringify(datasetData).length;
      // console.log(`[ChartBuilder] Dataset: ${dataset.name}, Data items: ${datasetData.length}, Approx. Bytes: ${currentDatasetSizeBytes}`);
      return acc + currentDatasetSizeBytes;
    }, 0);
    console.log('[ChartBuilder] Total calculated size in bytes:', usedInBytes);
    const usedInMB = usedInBytes / (1024 * 1024);
    console.log('[ChartBuilder] Total calculated size in MB (before toFixed):', usedInMB);
    const percentage = Math.min((usedInMB / 5.0) * 100, 100);
    return {
      used: usedInMB,
      total: 5.0,
      percentage: percentage,
    };
  }, [datasets]); // Only recalculate when datasets change

  return (
    <div className="flex h-screen overflow-hidden w-full">{/* Using overflow-hidden to prevent horizontal scrolling */}
      {/* MiniSidebar - Fixed width container */}
      <div className="flex-shrink-0 transition-all duration-300" style={{
        width: isSidebarCollapsed ? '48px' : '256px',
        minWidth: isSidebarCollapsed ? '48px' : '256px',
        maxWidth: isSidebarCollapsed ? '48px' : '256px',
        transition: 'width 0.3s ease, min-width 0.3s ease, max-width 0.3s ease'
      }}>
        <MiniSidebar
          savedDatasets={memoizedDatasets}
          storageInfo={storageInfoValue}
          isLoadingDatasets={isLoadingDatasets}
          onDatasetSelect={(dataset: DatasetItem) => {
            // Find the first cell and select this dataset for it
            if (cells.length > 0) {
              const firstCellId = cells[0].id;
              handleSelectDatasets(firstCellId, [dataset.id]).then(result => {
                if (result && result.datasetIds) {
                  // Update the cell's selectedDatasetIds
                  setCells(prev => prev.map(c =>
                    c.id === firstCellId ? { ...c, selectedDatasetIds: result.datasetIds } : c
                  ));
                }
              }).catch(err => console.error('Error selecting datasets:', err));
            }
          }}
          onDeleteDataset={(datasetId: string) => {
            toast.success('Dataset removed');
          }}
          onShowVersionHistory={(dataset: DatasetItem) => {
            toast.info(`Version history for ${dataset.name} is not available`);
          }}
          onUploadClick={() => {
            toast.info('Upload functionality is not available in this view');
          }}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={() => {
            setIsSidebarCollapsed(prev => {
              const newState = !prev;
              // Dispatch a custom event to notify components about the sidebar toggle
              setTimeout(() => {
                if (typeof window !== 'undefined') {
                  window.dispatchEvent(new CustomEvent('sidebar-toggle', { 
                    detail: { collapsed: newState } 
                  }));
                }
              }, 50);
              return newState;
            });
          }}
        />
      </div>

      <div className="flex-1 overflow-auto transition-all duration-300" style={{ width: 'calc(100% - ' + (isSidebarCollapsed ? '48px' : '256px') + ')', transition: 'width 0.3s ease', overflowX: 'hidden' }}>
        <div className="transition-all duration-300 w-full max-w-4xl mx-auto px-6 py-4"
             style={{
               maxWidth: '900px', // Jupyter-like max width
               width: '100%'
             }}>
          {isCheckingConnection && (
            <div className="bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-800 text-blue-800 dark:text-blue-200 px-3 py-2 rounded-md mb-3 flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-blue-500 animate-pulse"></span>
              <span className="text-sm font-medium">Checking server connection...</span>
            </div>
          )}
          {!isCheckingConnection && !isOnline && (
            <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-3 py-2 rounded-md mb-3 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="h-2 w-2 rounded-full bg-yellow-500 animate-pulse"></span>
                <span className="text-sm font-medium">Offline Mode - Server Unreachable</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-xs">
                  Using mock data. Only SQL queries are available.
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 px-2 text-xs"
                  onClick={checkConnectivity}
                  disabled={isCheckingConnection}
                >
                  <RefreshCw className={`h-3 w-3 ${isCheckingConnection ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </div>
          )}
          {!isCheckingConnection && isOnline && showOnlineStatus && (
            <div className="bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-800 text-green-800 dark:text-green-200 px-3 py-2 rounded-md mb-3 flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-green-500"></span>
              <span className="text-sm font-medium">Online - Connected to server</span>
            </div>
          )}

          <Tabs
            value={activeTab}
            onValueChange={(value) => {
              // Update URL when tab changes
              const params = new URLSearchParams(searchParams.toString())
              if (value === 'dashboard') {
                params.set('tab', 'dashboard')
              } else {
                params.delete('tab')
              }

              // Update active tab state
              setActiveTab(value);

              // Update URL without scrolling
              router.push(`?${params.toString()}`, { scroll: false })

              // Force a resize event after tab change to ensure charts render properly
              setTimeout(() => {
                if (typeof window !== 'undefined') {
                  window.dispatchEvent(new Event('resize'));
                }
              }, 100);
            }}
            className="w-full">
        <ChartBuilderHeader
          currentWorkspace={currentWorkspace}
          onWorkspaceChange={(workspace) => handleWorkspaceChange(workspace, setCells)}
          onSaveToWorkspace={handleSaveToWorkspace}
          isSaving={isSaving}
          activeTab={activeTab}
          datasetCache={datasetCache}
          isLoadingDatasets={isLoadingDatasets}
          refreshDatasets={refreshDatasets}
          isOnline={isOnline}
          isCheckingConnection={isCheckingConnection}
          checkConnectivity={checkConnectivity}
          cells={cells}
          savedCharts={savedCharts}
          onImport={handleNotebookImport}
          onAddCell={handleAddCell}
        />

        <TabsContent value="notebook" className="mt-0">
          {isLoadingCells ? (
            <div className="flex items-center justify-center h-[400px]">
              <div className="text-center space-y-4">
                <div className="relative">
                  <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/20 border-t-primary mx-auto"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-lg font-semibold">Loading Notebook</div>
                  <div className="text-sm text-muted-foreground">Fetching cells from workspace...</div>
                  <div className="flex items-center justify-center space-x-1">
                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="notebook-cells">
                {(provided) => (
                  <div
                    className="space-y-2"
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                  >
                    {cells.map((cell, index) => (
                    <Draggable key={cell.id} draggableId={cell.id} index={index}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={`${snapshot.isDragging ? 'opacity-70' : ''}`}
                        >
                          <Cell
                            key={cell.id}
                            id={cell.id}
                            content={cell.content}
                            language={cell.language}
                            cellType={cell.cellType}
                            result={{
                              data: cell.result?.data || [],
                              output: cell.result?.output,
                              plots: cell.result?.plots,
                              error: cell.error,
                              errorDetails: cell.errorDetails,
                              executionTime: cell.executionTime
                            }}
                            onRun={handleRunCell}
                            onDelete={handleDeleteCell}
                            onAddCell={handleAddCell}
                            onSelectDatasets={(datasetIds) => {
                              return new Promise<{ selectedList: Dataset[], datasetIds: string[] }>((resolve, reject) => {
                                handleSelectDatasets(cell.id, datasetIds).then(result => {
                                  if (result && result.datasetIds) {
                                    // Update the cell's selectedDatasetIds
                                    setCells(prev => prev.map(c =>
                                      c.id === cell.id ? { ...c, selectedDatasetIds: result.datasetIds } : c
                                    ));
                                    resolve(result);
                                  } else {
                                    resolve({ selectedList: [], datasetIds: [] });
                                  }
                                }).catch(err => {
                                  console.error('Error selecting datasets:', err);
                                  reject(err);
                                });
                              });
                            }}
                            onLanguageChange={(lang) => handleLanguageChange(cell.id, lang)}
                            selectedDatasets={(cell.selectedDatasetIds || []).map(id => datasetCache[id]).filter(Boolean)}
                            availableDatasets={datasets}
                            isSuccess={cell.isSuccess}
                            showGraphicWalker={cell.showGraphicWalker || false}
                            onSaveChart={handleSaveChart}
                            onSaveTable={handleSaveTable}
                            onSavePlot={handleSavePlot}
                            notes={cell.notes || '[]'}
                            onUpdateNotes={handleUpdateNotes}
                            index={index} // Pass the index to show cell number
                            dragHandleProps={provided.dragHandleProps} // Pass drag handle props
                            onMoveUp={moveCellUp} // Pass move up function
                            onMoveDown={moveCellDown} // Pass move down function
                            onContentChange={(value) => {
                              setCells(prev => prev.map(c =>
                                c.id === cell.id ? { ...c, content: value } : c
                              ));
                            }}
                            onConvertCellType={convertCellType} // Pass the convert cell type function
                          />
                        </div>
                      )}
                    </Draggable>
                  ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}
        </TabsContent>

        <TabsContent value="dashboard" className="mt-0 relative">
          {isLoadingDashboard && (
            <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="relative">
                  <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/20 border-t-primary mx-auto"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-lg font-semibold">Loading Dashboard Items</div>
                  <div className="text-sm text-muted-foreground">Fetching charts and visualizations...</div>
                  <div className="flex items-center justify-center space-x-1">
                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <Dashboard
            charts={savedCharts}
            items={[
              ...savedCharts,
              ...savedTables,
              ...savedPlots,
              ...savedCalculatorResults
            ]}
            onRemoveItem={(itemId) => {
              // Determine the type of item and call the appropriate handler
              if (itemId.startsWith('chart-')) {
                handleRemoveChart(itemId);
              } else if (itemId.startsWith('table-')) {
                useDashboardStore.getState().removeTable(itemId);
              } else if (itemId.startsWith('plot-')) {
                useDashboardStore.getState().removePlot(itemId);
              } else if (itemId.startsWith('calc-')) {
                useDashboardStore.getState().removeCalculatorResult(itemId);
              }
            }}
            onUpdateItem={(itemId, updates) => {
              // Determine the type of item and call the appropriate handler
              if (itemId.startsWith('chart-')) {
                // Cast updates to Partial<SavedChart> to ensure type compatibility
                handleUpdateChart(itemId, updates as unknown as Partial<SavedChart>);
              } else if (itemId.startsWith('table-')) {
                // Use the Zustand store's updateTable function
                useDashboardStore.getState().updateTable(itemId, updates as Partial<TableItem>);
              } else if (itemId.startsWith('plot-')) {
                // Use the Zustand store's updatePlot function
                useDashboardStore.getState().updatePlot(itemId, updates as Partial<PythonPlotItem>);
              } else if (itemId.startsWith('calc-')) {
                // Use the Zustand store's updateCalculatorResult function
                // @ts-ignore
                useDashboardStore.getState().updateCalculatorResult(itemId, updates);
              }
            }}
            onReorderItems={(newItems) => {
              // Extract charts, tables, plots, and calculator results from the new items with proper type casting
              const charts = newItems.filter(item => item.type === 'chart') as SavedChart[];
              const tables = newItems.filter(item => item.type === 'table') as TableItem[];
              const plots = newItems.filter(item => item.type === 'pythonplot') as PythonPlotItem[];
              const calculatorResults = newItems.filter(item => item.type === 'calculator');

              // Use the Zustand store to update all items
              const store = useDashboardStore.getState();

              // Clear current items and add the reordered ones
              store.clearAll();

              // Add all items back with their new positions
              charts.forEach(chart => store.addChart(chart));
              tables.forEach(table => store.addTable(table));
              plots.forEach(plot => store.addPlot(plot));
              // @ts-ignore
              calculatorResults.forEach(calc => store.addCalculatorResult(calc));
            }}
            onRemoveChart={handleRemoveChart}
            onUpdateChart={handleUpdateChart}
            onReorderCharts={handleReorderCharts}
            onAddChart={(chart) => {
              // Ensure the chart has all required properties
              const validChart: SavedChart = {
                ...chart,
                type: 'chart',
                title: chart.title || 'Untitled Chart',
                description: chart.description || '',
                gridColumn: chart.gridColumn || 0,
                gridRow: chart.gridRow || 0,
                width: chart.width || 4,
                height: chart.height || 3,
                chartType: chart.chartType || 'bar',
                data: chart.data || [],
                config: chart.config || {},
                createdAt: chart.createdAt || new Date()
              };
              // Add to the Zustand store
              useDashboardStore.getState().addChart(validChart);
            }}
          />
        </TabsContent>
      </Tabs>
        </div>
      </div>
    </div>
  )
}

